package com.cdkit.modules.cm.performance.budget;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.api.budget.IAnnualBudgetApi;
import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDTO;
import com.cdkit.modules.cm.application.budget.AnnualBudgetApplication;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 年度总预算控制器
 * <AUTHOR>
 * @date 2025-07-30
 */
@Tag(name = "年度总预算管理")
@RestController
@RequestMapping("/cm/costAnnualBudget")
@RequiredArgsConstructor
@Slf4j
public class CostAnnualBudgetController implements IAnnualBudgetApi {

    private final AnnualBudgetApplication annualBudgetApplication;

    @Override
    public Result<IPage<CostAnnualBudgetDTO>> queryPageList(CostAnnualBudgetDTO queryVO, Integer pageNo, Integer pageSize) {
        log.info("开始分页查询年度总预算列表，页码: {}, 每页数量: {}", pageNo, pageSize);
        
        try {
            // 转换查询条件
            CostAnnualBudgetEntity queryEntity = CostAnnualBudgetConverter.toEntity(queryVO);
            
            // 分页查询
            PageRes<CostAnnualBudgetEntity> pageRes = annualBudgetApplication.queryPageList(queryEntity, pageNo, pageSize);
            
            // 使用 MyBatis Plus 的分页对象
            IPage<CostAnnualBudgetDTO> page = new Page<>(pageNo, pageSize);
            if (pageRes != null) {
                page.setCurrent(pageRes.getCurrent());
                page.setSize(pageRes.getSize());
                page.setTotal(pageRes.getTotal());
                page.setRecords(CostAnnualBudgetConverter.toDTOList(pageRes.getRecords()));
            }
            
            log.info("分页查询年度总预算列表成功，总记录数: {}", page.getTotal());
            return Result.OK(page);
            
        } catch (Exception e) {
            log.error("分页查询年度总预算列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<CostAnnualBudgetDTO> queryById(String id) {
        log.info("开始根据ID查询年度总预算详情，ID: {}", id);
        
        try {
            CostAnnualBudgetEntity entity = annualBudgetApplication.queryById(id);
            if (entity == null) {
                log.warn("年度总预算不存在，ID: {}", id);
                return Result.error("年度总预算不存在");
            }
            
            CostAnnualBudgetDTO dto = CostAnnualBudgetConverter.toDTO(entity);
            log.info("根据ID查询年度总预算详情成功，ID: {}, 预算编号: {}", id, dto.getBudgetCode());
            return Result.OK(dto);
            
        } catch (Exception e) {
            log.error("根据ID查询年度总预算详情失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> add(CostAnnualBudgetDTO costAnnualBudget) {
        log.info("开始新增年度总预算，预算名称: {}", costAnnualBudget != null ? costAnnualBudget.getBudgetName() : "null");
        
        try {
            if (costAnnualBudget == null) {
                return Result.error("年度总预算数据不能为空");
            }
            
            CostAnnualBudgetEntity entity = CostAnnualBudgetConverter.toEntity(costAnnualBudget);
            String id = annualBudgetApplication.add(entity);
            
            log.info("新增年度总预算成功，预算名称: {}, ID: {}", costAnnualBudget.getBudgetName(), id);
            return Result.OK("新增成功");
            
        } catch (Exception e) {
            log.error("新增年度总预算失败，预算名称: {}", costAnnualBudget != null ? costAnnualBudget.getBudgetName() : "null", e);
            return Result.error("新增失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> edit(CostAnnualBudgetDTO costAnnualBudget) {
        log.info("开始编辑年度总预算，ID: {}, 预算名称: {}", 
                costAnnualBudget != null ? costAnnualBudget.getId() : "null",
                costAnnualBudget != null ? costAnnualBudget.getBudgetName() : "null");
        
        try {
            if (costAnnualBudget == null) {
                return Result.error("年度总预算数据不能为空");
            }
            
            CostAnnualBudgetEntity entity = CostAnnualBudgetConverter.toEntity(costAnnualBudget);
            String id = annualBudgetApplication.edit(entity);
            
            log.info("编辑年度总预算成功，ID: {}, 预算名称: {}", id, costAnnualBudget.getBudgetName());
            return Result.OK("编辑成功");
            
        } catch (Exception e) {
            log.error("编辑年度总预算失败，ID: {}, 预算名称: {}", 
                    costAnnualBudget != null ? costAnnualBudget.getId() : "null",
                    costAnnualBudget != null ? costAnnualBudget.getBudgetName() : "null", e);
            return Result.error("编辑失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> delete(String id) {
        log.info("开始根据ID删除年度总预算，ID: {}", id);
        
        try {
            annualBudgetApplication.delete(id);
            log.info("根据ID删除年度总预算成功，ID: {}", id);
            return Result.OK("删除成功");
            
        } catch (Exception e) {
            log.error("根据ID删除年度总预算失败，ID: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> deleteBatch(String ids) {
        log.info("开始批量删除年度总预算，IDs: {}", ids);

        try {
            annualBudgetApplication.deleteBatch(ids);
            log.info("批量删除年度总预算成功，IDs: {}", ids);
            return Result.OK("批量删除成功");

        } catch (Exception e) {
            log.error("批量删除年度总预算失败，IDs: {}", ids, e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> generateNextBudgetCode(String year) {
        log.info("开始生成下一个预算编号，年份: {}", year);

        try {
            String nextBudgetCode = annualBudgetApplication.generateNextBudgetCode(year);
            log.info("生成下一个预算编号成功，年份: {}, 预算编号: {}", year, nextBudgetCode);
            return Result.OK(nextBudgetCode);

        } catch (Exception e) {
            log.error("生成下一个预算编号失败，年份: {}", year, e);
            return Result.error("生成预算编号失败：" + e.getMessage());
        }
    }


}
