package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.IBaseDomainRepository;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;

import java.util.List;

/**
 * 年度总预算仓储接口
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface CostAnnualBudgetRepository extends IBaseDomainRepository<CostAnnualBudgetEntity, CostAnnualBudgetEntity> {

    /**
     * 保存年度总预算
     *
     * @param entity 年度总预算实体
     * @return 保存结果
     */
    CostAnnualBudgetEntity save(CostAnnualBudgetEntity entity);

    /**
     * 根据ID更新年度总预算
     *
     * @param entity 年度总预算实体
     * @return 更新结果
     */
    CostAnnualBudgetEntity updateById(CostAnnualBudgetEntity entity);

    /**
     * 根据ID删除年度总预算
     *
     * @param id 年度总预算ID
     */
    void deleteById(String id);

    /**
     * 批量删除年度总预算
     *
     * @param ids 年度总预算ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 根据ID查询年度总预算
     *
     * @param id 年度总预算ID
     * @return 年度总预算实体
     */
    CostAnnualBudgetEntity findById(String id);

    /**
     * 根据ID列表查询年度总预算
     *
     * @param ids 年度总预算ID列表
     * @return 年度总预算实体列表
     */
    List<CostAnnualBudgetEntity> findByIds(List<String> ids);

    /**
     * 分页查询年度总预算
     *
     * @param queryEntity 查询条件
     * @param pageReq 分页参数
     * @return 分页结果
     */
    PageRes<CostAnnualBudgetEntity> queryPageList(CostAnnualBudgetEntity queryEntity, PageReq pageReq);

    /**
     * 根据预算编号查询年度总预算
     *
     * @param budgetCode 预算编号
     * @return 年度总预算实体
     */
    CostAnnualBudgetEntity findByBudgetCode(String budgetCode);

    /**
     * 根据年份查询年度总预算列表
     *
     * @param budgetYear 年份
     * @return 年度总预算实体列表
     */
    List<CostAnnualBudgetEntity> findByBudgetYear(String budgetYear);

    /**
     * 根据状态查询年度总预算列表
     *
     * @param budgetStatus 预算状态
     * @return 年度总预算实体列表
     */
    List<CostAnnualBudgetEntity> findByBudgetStatus(String budgetStatus);
}
