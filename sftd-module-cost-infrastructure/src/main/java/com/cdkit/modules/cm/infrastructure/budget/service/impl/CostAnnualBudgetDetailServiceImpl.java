package com.cdkit.modules.cm.infrastructure.budget.service.impl;


import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetail;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostAnnualBudgetDetailMapper;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 项目年度预算
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
@Service
public class CostAnnualBudgetDetailServiceImpl extends ServiceImpl<CostAnnualBudgetDetailMapper, CostAnnualBudgetDetail> implements ICostAnnualBudgetDetailService {
	
	@Autowired
	private CostAnnualBudgetDetailMapper costAnnualBudgetDetailMapper;
	
	@Override
	public List<CostAnnualBudgetDetail> selectByMainId(String mainId) {
		return costAnnualBudgetDetailMapper.selectByMainId(mainId);
	}
}
