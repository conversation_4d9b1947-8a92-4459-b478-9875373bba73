package com.cdkit.modules.cm.infrastructure.budget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetail;

import java.util.List;

/**
 * @Description: 项目年度预算
 * @Author: cdkit-boot
 * @Date:   2025-07-30
 * @Version: V1.0
 */
public interface ICostAnnualBudgetDetailService extends IService<CostAnnualBudgetDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostAnnualBudgetDetail>
	 */
	public List<CostAnnualBudgetDetail> selectByMainId(String mainId);
}
